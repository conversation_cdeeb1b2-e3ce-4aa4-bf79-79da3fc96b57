{"version": 3, "file": "register.js", "sources": ["pages/register/register.vue", "C:/Users/<USER>/Downloads/HBuilderX.4.66.2025051912/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcmVnaXN0ZXIvcmVnaXN0ZXIudnVl"], "sourcesContent": ["<template>\n  <view class=\"register-container\">\n    <!-- 页面头部提示 -->\n    <view class=\"register-header\">\n      <text class=\"page-title\">完善个人资料</text>\n      <text class=\"page-desc\">请填写真实有效的个人信息，以便机构审核</text>\n    </view>\n\n    <!-- 注册表单 -->\n    <view class=\"register-form\">\n      <u-form\n        ref=\"formRef\"\n        :model=\"formData\"\n        :rules=\"formRules\"\n        labelPosition=\"top\"\n        labelWidth=\"auto\"\n      >\n        <!-- 真实姓名 -->\n        <u-form-item label=\"真实姓名\" prop=\"realName\" required>\n          <u-input\n            v-model=\"formData.realName\"\n            placeholder=\"请输入真实姓名\"\n            clearable\n            maxlength=\"20\"\n          />\n        </u-form-item>\n\n        <!-- 联系电话 -->\n        <u-form-item label=\"联系电话\" prop=\"phone\" required>\n          <u-input\n            v-model=\"formData.phone\"\n            placeholder=\"请输入手机号码\"\n            type=\"number\"\n            clearable\n            maxlength=\"11\"\n          />\n        </u-form-item>\n\n        <!-- 身份证号 -->\n        <u-form-item label=\"身份证号码\" prop=\"idCard\" required>\n          <u-input\n            v-model=\"formData.idCard\"\n            placeholder=\"请输入身份证号码\"\n            clearable\n            maxlength=\"18\"\n          />\n        </u-form-item>\n\n        <!-- 隶属机构 -->\n        <u-form-item label=\"隶属机构\" prop=\"institutionId\" required @click=\"selectInstitution\">\n          <u-input\n            v-model=\"institutionDisplayName\"\n            placeholder=\"请选择所属机构\"\n            readonly\n            suffixIcon=\"arrow-down\"\n            :key=\"`institution-${updateKey}`\"\n          />\n        </u-form-item>\n\n        <!-- 职位 -->\n        <u-form-item label=\"职位\" prop=\"positionId\" required @click=\"selectPosition\">\n          <u-input\n            v-model=\"positionDisplayName\"\n            placeholder=\"请选择职位\"\n            readonly\n            suffixIcon=\"arrow-down\"\n            :key=\"`position-${updateKey}`\"\n          />\n        </u-form-item>\n\n        <!-- 本人照片 -->\n        <u-form-item label=\"本人照片\" prop=\"avatar\" required>\n          <view class=\"avatar-upload\">\n            <view v-if=\"!formData.avatar\" class=\"upload-placeholder\" @click=\"uploadAvatar\">\n              <u-icon name=\"camera\" size=\"60\" color=\"#cccccc\" />\n              <text class=\"upload-text\">点击上传本人正面照片</text>\n              <text class=\"upload-desc\">用于人脸识别验证</text>\n            </view>\n            <view v-else class=\"avatar-preview\" @click=\"uploadAvatar\">\n              <image :src=\"formData.avatar\" mode=\"aspectFill\" class=\"avatar-image\" />\n              <view class=\"avatar-mask\">\n                <u-icon name=\"camera\" size=\"40\" color=\"#ffffff\" />\n              </view>\n            </view>\n          </view>\n        </u-form-item>\n      </u-form>\n    </view>\n\n    <!-- 操作按钮 -->\n    <view class=\"register-actions\">\n      <u-button \n        type=\"primary\"\n        :loading=\"isSubmitting\"\n        loadingText=\"提交中...\"\n        @click=\"submitForm\"\n        customStyle=\"width: 100%; margin-bottom: 24rpx;\"\n      >\n        提交审核\n      </u-button>\n      \n      <u-button \n        type=\"info\"\n        plain\n        @click=\"skipRegister\"\n        customStyle=\"width: 100%;\"\n      >\n        跳过，先去学习\n      </u-button>\n    </view>\n  </view>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, reactive, onMounted } from 'vue';\nimport { storeToRefs } from 'pinia';\nimport { useUserStore } from '@/src/stores/modules/user';\nimport { submitUserInfo, getInstitutions, getPositions } from '@/src/api/modules/user';\nimport type { RegisterParams, Institution, Position } from '@/src/types/api';\n\n// ==================== Store ====================\nconst userStore = useUserStore();\nconst { profile } = storeToRefs(userStore);\nconst { updateProfile } = userStore;\n\n// ==================== 响应式数据 ====================\n/** 表单引用 */\nconst formRef = ref();\n/** 提交状态 */\nconst isSubmitting = ref<boolean>(false);\n\n/** 机构数据 */\nconst institutions = ref<Institution[]>([]);\n/** 职位数据 */\nconst positions = ref<Position[]>([]);\n/** 显示的机构名称 */\nconst institutionDisplayName = ref<string>('');\n/** 显示的职位名称 */\nconst positionDisplayName = ref<string>('');\n/** 强制更新的key */\nconst updateKey = ref<number>(0);\n\n/** 表单数据 */\nconst formData = reactive<RegisterParams>({\n  realName: '',\n  phone: '',\n  idCard: '',\n  institutionId: '',\n  positionId: '',\n  avatar: '',\n});\n\n\n\n/** 表单验证规则 */\nconst formRules = reactive({\n  realName: [\n    { required: true, message: '请输入真实姓名', trigger: 'blur' },\n    { min: 2, max: 20, message: '姓名长度在2-20个字符', trigger: 'blur' },\n  ],\n  phone: [\n    { required: true, message: '请输入手机号码', trigger: 'blur' },\n    { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' },\n  ],\n  idCard: [\n    { required: true, message: '请输入身份证号码', trigger: 'blur' },\n    { pattern: /(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)/, message: '请输入正确的身份证号码', trigger: 'blur' },\n  ],\n  institutionId: [\n    { required: true, message: '请选择隶属机构', trigger: 'change' },\n  ],\n  positionId: [\n    { required: true, message: '请选择职位', trigger: 'change' },\n  ],\n  avatar: [\n    { required: true, message: '请上传本人照片', trigger: 'change' },\n  ],\n});\n\n// ==================== 生命周期 ====================\nonMounted(() => {\n  loadInitialData();\n});\n\n// ==================== 事件处理 ====================\n/**\n * 加载初始数据\n */\nasync function loadInitialData(): Promise<void> {\n  try {\n    uni.showLoading({ title: '加载中...' });\n\n    uni.__f__('log','at pages/register/register.vue:193','开始加载机构和职位数据...');\n\n    const [institutionsData, positionsData] = await Promise.all([\n      getInstitutions(),\n      getPositions(),\n    ]);\n\n    uni.__f__('log','at pages/register/register.vue:200','机构数据:', institutionsData);\n    uni.__f__('log','at pages/register/register.vue:201','职位数据:', positionsData);\n\n    // 确保数据是数组格式\n    if (Array.isArray(institutionsData)) {\n      institutions.value = institutionsData;\n      uni.__f__('log','at pages/register/register.vue:206','机构数据设置成功，数量:', institutions.value.length);\n    } else {\n      uni.__f__('error','at pages/register/register.vue:208','机构数据不是数组格式:', institutionsData);\n      institutions.value = [];\n    }\n\n    if (Array.isArray(positionsData)) {\n      positions.value = positionsData;\n      uni.__f__('log','at pages/register/register.vue:214','职位数据设置成功，数量:', positions.value.length);\n    } else {\n      uni.__f__('error','at pages/register/register.vue:216','职位数据不是数组格式:', positionsData);\n      positions.value = [];\n    }\n\n    uni.hideLoading();\n  } catch (error) {\n    uni.hideLoading();\n    uni.__f__('error','at pages/register/register.vue:223','加载机构和职位数据失败:', error);\n\n    // 设置默认空数组，避免后续调用find方法时出错\n    institutions.value = [];\n    positions.value = [];\n\n    uni.showToast({\n      title: '数据加载失败，请重试',\n      icon: 'none',\n    });\n  }\n}\n\n/**\n * 选择机构\n */\nfunction selectInstitution(): void {\n  if (!Array.isArray(institutions.value) || institutions.value.length === 0) {\n    uni.showToast({\n      title: '机构数据尚未加载',\n      icon: 'none',\n    });\n    return;\n  }\n\n  uni.__f__('log','at pages/register/register.vue:248','显示机构选择器，可选机构:', institutions.value);\n\n  uni.showActionSheet({\n    itemList: institutions.value.map(item => item.name),\n    success: (res) => {\n      const selectedInstitution = institutions.value[res.tapIndex];\n      formData.institutionId = selectedInstitution.id;\n      institutionDisplayName.value = selectedInstitution.name;\n      updateKey.value++; // 强制更新组件\n      uni.__f__('log','at pages/register/register.vue:257','选择了机构:', selectedInstitution);\n    },\n  });\n}\n\n/**\n * 选择职位\n */\nfunction selectPosition(): void {\n  if (!Array.isArray(positions.value) || positions.value.length === 0) {\n    uni.showToast({\n      title: '职位数据尚未加载',\n      icon: 'none',\n    });\n    return;\n  }\n\n  uni.__f__('log','at pages/register/register.vue:274','显示职位选择器，可选职位:', positions.value);\n\n  uni.showActionSheet({\n    itemList: positions.value.map(item => item.name),\n    success: (res) => {\n      const selectedPosition = positions.value[res.tapIndex];\n      formData.positionId = selectedPosition.id;\n      positionDisplayName.value = selectedPosition.name;\n      updateKey.value++; // 强制更新组件\n      uni.__f__('log','at pages/register/register.vue:283','选择了职位:', selectedPosition);\n    },\n  });\n}\n\n/**\n * 上传头像\n */\nfunction uploadAvatar(): void {\n  uni.chooseImage({\n    count: 1,\n    sizeType: ['compressed'],\n    sourceType: ['camera', 'album'],\n    success: async (res) => {\n      try {\n        uni.showLoading({ title: '上传中...' });\n        \n        // 这里应该调用上传API，暂时直接使用本地路径\n        formData.avatar = res.tempFilePaths[0];\n        \n        uni.hideLoading();\n        uni.showToast({\n          title: '上传成功',\n          icon: 'success',\n        });\n      } catch (error) {\n        uni.hideLoading();\n        uni.showToast({\n          title: '上传失败，请重试',\n          icon: 'none',\n        });\n      }\n    },\n  });\n}\n\n/**\n * 提交表单\n */\nasync function submitForm(): Promise<void> {\n  try {\n    // 表单验证\n    await formRef.value.validate();\n    \n    isSubmitting.value = true;\n    \n    // 提交注册信息\n    await submitUserInfo(formData);\n    \n    // 更新用户状态为待审核\n    updateProfile({ status: 'pending_review' });\n    \n    uni.showToast({\n      title: '提交成功，请等待审核',\n      icon: 'success',\n      duration: 2000,\n    });\n    \n    // 跳转到个人中心\n    setTimeout(() => {\n      uni.reLaunch({ url: '/pages/profile/profile' });\n    }, 2000);\n    \n  } catch (error) {\n    uni.__f__('error','at pages/register/register.vue:347','提交失败:', error);\n    uni.showToast({\n      title: '提交失败，请重试',\n      icon: 'none',\n    });\n  } finally {\n    isSubmitting.value = false;\n  }\n}\n\n/**\n * 跳过注册\n */\nfunction skipRegister(): void {\n  uni.showModal({\n    title: '确认跳过',\n    content: '跳过后您将无法参加考试，只能进行学习练习，确认跳过吗？',\n    success: (res) => {\n      if (res.confirm) {\n        // 跳转到学习中心\n        uni.reLaunch({ url: '/pages/study/study' });\n      }\n    },\n  });\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '@/src/styles/variables.scss';\n\n/* ==================== 主容器 ==================== */\n.register-container {\n  min-height: 100vh;\n  background-color: $background-color;\n  padding: $spacing-lg;\n}\n\n/* ==================== 头部区域 ==================== */\n.register-header {\n  text-align: center;\n  margin-bottom: $spacing-xl;\n}\n\n.page-title {\n  display: block;\n  font-size: 36rpx;\n  color: #1976d2;\n  font-weight: bold;\n  margin-bottom: $spacing-sm;\n}\n\n.page-desc {\n  display: block;\n  font-size: 28rpx;\n  color: #757575;\n}\n\n/* ==================== 表单区域 ==================== */\n.register-form {\n  margin-bottom: $spacing-xl;\n}\n\n/* ==================== 头像上传 ==================== */\n.avatar-upload {\n  width: 200rpx;\n  height: 200rpx;\n  border-radius: $border-radius-medium;\n  overflow: hidden;\n  border: 2rpx dashed $divider-color;\n}\n\n.upload-placeholder {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  background-color: $surface-color;\n}\n\n.upload-text {\n  font-size: 26rpx;\n  color: #999999;\n  margin-top: 16rpx;\n}\n\n.upload-desc {\n  font-size: 22rpx;\n  color: #cccccc;\n  margin-top: 8rpx;\n}\n\n.avatar-preview {\n  width: 100%;\n  height: 100%;\n  position: relative;\n}\n\n.avatar-image {\n  width: 100%;\n  height: 100%;\n}\n\n.avatar-mask {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.avatar-preview:active .avatar-mask {\n  opacity: 1;\n}\n\n/* ==================== 操作按钮 ==================== */\n.register-actions {\n  padding: $spacing-md 0;\n}\n</style>\n", "import MiniProgramPage from 'E:/project/CDCExamA/pages/register/register.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "storeToRefs", "ref", "reactive", "onMounted", "uni", "getInstitutions", "getPositions", "submitUserInfo"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAyHA,UAAM,YAAYA,wBAAAA;AACEC,kBAAAA,YAAY,SAAS;AACnC,UAAA,EAAE,cAAkB,IAAA;AAI1B,UAAM,UAAUC,cAAAA;AAEV,UAAA,eAAeA,kBAAa,KAAK;AAGjC,UAAA,eAAeA,kBAAmB,CAAA,CAAE;AAEpC,UAAA,YAAYA,kBAAgB,CAAA,CAAE;AAE9B,UAAA,yBAAyBA,kBAAY,EAAE;AAEvC,UAAA,sBAAsBA,kBAAY,EAAE;AAEpC,UAAA,YAAYA,kBAAY,CAAC;AAG/B,UAAM,WAAWC,cAAAA,SAAyB;AAAA,MACxC,UAAU;AAAA,MACV,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,eAAe;AAAA,MACf,YAAY;AAAA,MACZ,QAAQ;AAAA,IAAA,CACT;AAKD,UAAM,YAAYA,cAAAA,SAAS;AAAA,MACzB,UAAU;AAAA,QACR,EAAE,UAAU,MAAM,SAAS,WAAW,SAAS,OAAO;AAAA,QACtD,EAAE,KAAK,GAAG,KAAK,IAAI,SAAS,gBAAgB,SAAS,OAAO;AAAA,MAC9D;AAAA,MACA,OAAO;AAAA,QACL,EAAE,UAAU,MAAM,SAAS,WAAW,SAAS,OAAO;AAAA,QACtD,EAAE,SAAS,iBAAiB,SAAS,cAAc,SAAS,OAAO;AAAA,MACrE;AAAA,MACA,QAAQ;AAAA,QACN,EAAE,UAAU,MAAM,SAAS,YAAY,SAAS,OAAO;AAAA,QACvD,EAAE,SAAS,4CAA4C,SAAS,eAAe,SAAS,OAAO;AAAA,MACjG;AAAA,MACA,eAAe;AAAA,QACb,EAAE,UAAU,MAAM,SAAS,WAAW,SAAS,SAAS;AAAA,MAC1D;AAAA,MACA,YAAY;AAAA,QACV,EAAE,UAAU,MAAM,SAAS,SAAS,SAAS,SAAS;AAAA,MACxD;AAAA,MACA,QAAQ;AAAA,QACN,EAAE,UAAU,MAAM,SAAS,WAAW,SAAS,SAAS;AAAA,MAC1D;AAAA,IAAA,CACD;AAGDC,kBAAAA,UAAU,MAAM;AACE;IAAA,CACjB;AAMD,mBAAe,kBAAiC;AAC1C,UAAA;AACFC,sBAAAA,MAAI,YAAY,EAAE,OAAO,SAAU,CAAA;AAE/BA,sBAAAA,MAAA,MAAM,OAAM,sCAAqC,gBAAgB;AAErE,cAAM,CAAC,kBAAkB,aAAa,IAAI,MAAM,QAAQ,IAAI;AAAA,UAC1DC,qCAAgB;AAAA,UAChBC,kCAAa;AAAA,QAAA,CACd;AAEDF,sBAAA,MAAI,MAAM,OAAM,sCAAqC,SAAS,gBAAgB;AAC9EA,sBAAA,MAAI,MAAM,OAAM,sCAAqC,SAAS,aAAa;AAGvE,YAAA,MAAM,QAAQ,gBAAgB,GAAG;AACnC,uBAAa,QAAQ;AACrBA,8BAAI,MAAM,OAAM,sCAAqC,gBAAgB,aAAa,MAAM,MAAM;AAAA,QAAA,OACzF;AACLA,wBAAA,MAAI,MAAM,SAAQ,sCAAqC,eAAe,gBAAgB;AACtF,uBAAa,QAAQ;QACvB;AAEI,YAAA,MAAM,QAAQ,aAAa,GAAG;AAChC,oBAAU,QAAQ;AAClBA,8BAAI,MAAM,OAAM,sCAAqC,gBAAgB,UAAU,MAAM,MAAM;AAAA,QAAA,OACtF;AACLA,wBAAA,MAAI,MAAM,SAAQ,sCAAqC,eAAe,aAAa;AACnF,oBAAU,QAAQ;QACpB;AAEAA,sBAAA,MAAI,YAAY;AAAA,eACT,OAAO;AACdA,sBAAA,MAAI,YAAY;AAChBA,sBAAA,MAAI,MAAM,SAAQ,sCAAqC,gBAAgB,KAAK;AAG5E,qBAAa,QAAQ;AACrB,kBAAU,QAAQ;AAElBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AAAA,MACH;AAAA,IACF;AAKA,aAAS,oBAA0B;AAC7B,UAAA,CAAC,MAAM,QAAQ,aAAa,KAAK,KAAK,aAAa,MAAM,WAAW,GAAG;AACzEA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AACD;AAAA,MACF;AAEAA,oBAAA,MAAI,MAAM,OAAM,sCAAqC,iBAAiB,aAAa,KAAK;AAExFA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,aAAa,MAAM,IAAI,CAAA,SAAQ,KAAK,IAAI;AAAA,QAClD,SAAS,CAAC,QAAQ;AAChB,gBAAM,sBAAsB,aAAa,MAAM,IAAI,QAAQ;AAC3D,mBAAS,gBAAgB,oBAAoB;AAC7C,iCAAuB,QAAQ,oBAAoB;AACzC,oBAAA;AACVA,wBAAA,MAAI,MAAM,OAAM,sCAAqC,UAAU,mBAAmB;AAAA,QACpF;AAAA,MAAA,CACD;AAAA,IACH;AAKA,aAAS,iBAAuB;AAC1B,UAAA,CAAC,MAAM,QAAQ,UAAU,KAAK,KAAK,UAAU,MAAM,WAAW,GAAG;AACnEA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AACD;AAAA,MACF;AAEAA,oBAAA,MAAI,MAAM,OAAM,sCAAqC,iBAAiB,UAAU,KAAK;AAErFA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB,UAAU,UAAU,MAAM,IAAI,CAAA,SAAQ,KAAK,IAAI;AAAA,QAC/C,SAAS,CAAC,QAAQ;AAChB,gBAAM,mBAAmB,UAAU,MAAM,IAAI,QAAQ;AACrD,mBAAS,aAAa,iBAAiB;AACvC,8BAAoB,QAAQ,iBAAiB;AACnC,oBAAA;AACVA,wBAAA,MAAI,MAAM,OAAM,sCAAqC,UAAU,gBAAgB;AAAA,QACjF;AAAA,MAAA,CACD;AAAA,IACH;AAKA,aAAS,eAAqB;AAC5BA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,UAAU,CAAC,YAAY;AAAA,QACvB,YAAY,CAAC,UAAU,OAAO;AAAA,QAC9B,SAAS,OAAO,QAAQ;AAClB,cAAA;AACFA,0BAAAA,MAAI,YAAY,EAAE,OAAO,SAAU,CAAA;AAG1B,qBAAA,SAAS,IAAI,cAAc,CAAC;AAErCA,0BAAA,MAAI,YAAY;AAChBA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YAAA,CACP;AAAA,mBACM,OAAO;AACdA,0BAAA,MAAI,YAAY;AAChBA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YAAA,CACP;AAAA,UACH;AAAA,QACF;AAAA,MAAA,CACD;AAAA,IACH;AAKA,mBAAe,aAA4B;AACrC,UAAA;AAEI,cAAA,QAAQ,MAAM;AAEpB,qBAAa,QAAQ;AAGrB,cAAMG,qBAAAA,eAAe,QAAQ;AAGf,sBAAA,EAAE,QAAQ,iBAAA,CAAkB;AAE1CH,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAAA,CACX;AAGD,mBAAW,MAAM;AACfA,wBAAAA,MAAI,SAAS,EAAE,KAAK,yBAA0B,CAAA;AAAA,WAC7C,GAAI;AAAA,eAEA,OAAO;AACdA,sBAAA,MAAI,MAAM,SAAQ,sCAAqC,SAAS,KAAK;AACrEA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AAAA,MAAA,UACD;AACA,qBAAa,QAAQ;AAAA,MACvB;AAAA,IACF;AAKA,aAAS,eAAqB;AAC5BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AAEfA,0BAAAA,MAAI,SAAS,EAAE,KAAK,qBAAsB,CAAA;AAAA,UAC5C;AAAA,QACF;AAAA,MAAA,CACD;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjXA,GAAG,WAAW,eAAe;"}